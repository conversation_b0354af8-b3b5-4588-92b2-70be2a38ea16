import asyncio
import json
from typing import Any, Dict, List, Optional
from services.exports.generate_docx_bytes import DocxBytesGenerator
from services.exports.generate_pdf_bytes import PDFGenerator as PDFGeneratorBytes
from services.exports.render_markdown import MarkdownRenderer
from services.proposal.document_title_extraction_service import DocumentTitleExtractionService
from controllers.customer.datametastore_controller import DataMetastoreController
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from models.customer_models import ProposalQueue, ProposalOutlineQueue
from services.proposal.utilities import ProposalUtilities
from database import get_customer_db, get_kontratar_db
from loguru import logger
from services.queue_service.proposal_queue_service import ProposalQueueService
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.proposal.rfi.rfi_generation_service import RFIGenerationService
from services.proposal.outline import ProposalOutlineService
from services.scheduler_service.schedule_lock_service import ScheduleLockService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.sam_opps_table_coontroller import OppsController
from controllers.customer.tenant_controller import TenantController
from sqlalchemy import select, update
from datetime import datetime
from services.proposal.constructive_criticism import generate_proposal_criticism
import os

class ProposalSchedulerService:
    """Scheduler service for processing proposal queue items"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False  # Set to True to enable processing
        self.rfi_generation_service = RFIGenerationService()
        self.rfp_generation_service = RFPGenerationService()
        self.outline_service = ProposalOutlineService()
        self.proposal_queue_service = ProposalQueueService
        self.lock_service = ScheduleLockService("PROPOSAL_SCHEDULER_LOCK", "PROPOSAL_SCHEDULER")
    
    async def process_proposal_queue(self):
        """Process new proposal queue items"""
        if not self.is_enabled:
            logger.info("Proposal scheduler is disabled, skipping processing")
            return
        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Proposal scheduler lock not acquired, skipping this run")
            return
            
        try:
            async for db in get_customer_db():
                items = await self.proposal_queue_service.get_proposal_queue_items(db, limit=5)
                
                if not items:
                    logger.info("No new proposal queue items found")
                    return
                
                logger.info(f"Processing {len(items)} new proposal queue items")
                
                for item in items:
                    try:
                        job_id = str(item.job_id)
                        # Update status to PROCESSING
                        await self.proposal_queue_service.update_proposal_queue_status(
                            db, job_id , "PROCESSING", "AI_PROCESSING"
                        )
                        
                        # Process the item (implement your AI processing logic here)
                        success = await self._process_proposal_item(item)
                        
                        if success:
                            # Update status to COMPLETED
                            await self.proposal_queue_service.update_proposal_queue_status(
                                db, job_id, "COMPLETED", "AI_COMPLETED"
                            )
                            logger.info(f"Successfully processed proposal queue item {item.job_id}")
                        else:
                            
                            pass
                       
                        
                    except Exception as e:
                        logger.error(f"Error processing proposal queue item {item.job_id}: {e}")
                        # Update status to FAILED
                        await self.proposal_queue_service.update_proposal_queue_status(
                            db, job_id, "FAILED", f"AI_ERROR: {str(e)}"
                        )
                        
        except Exception as e:
            logger.error(f"Error in process_proposal_queue: {e}")
        finally:
            await self.lock_service.release_lock()
    
    async def _process_proposal_item(self, item: ProposalQueue):
        """Process a proposal queue item"""
        # This is where you would implement your AI processing logic
        # For now, we'll just simulate some processing time
        logger.info(f"Processing proposal item: {item.job_id}")
        logger.info(f"Job instruction: {item.job_instruction}")
        logger.info(f"Opps ID: {item.opps_id}")
        logger.info(f"Tenant ID: {item.tenant_id}")
        logger.info(f"Request type: {item.request_type}")
        
        job_instruction = str(item.job_instruction)
        job_requested_by = str(item.job_submitted_by)
        job = json.loads(job_instruction)
        is_rfp = job.get("isRFP", None)

        # Handle missing is_rfp field gracefully
        if is_rfp is None:
            logger.warning(f"isRFP field missing in job instruction for {item.job_id}, attempting to determine from opportunity type")
            opportunity_type = job.get("opportunityType", "")

            from services.proposal.utilities import ProposalUtilities
            is_rfp = ProposalUtilities.is_rfp(opportunity_type)
            logger.info(f"Determined is_rfp={is_rfp} from opportunity type: {opportunity_type}")

        # Check outline availability before proceeding with proposal generation
        outline_ready = await self._check_outline_availability(
            item.opps_id,
            item.tenant_id,
            job.get("opportunityType", "")
        )

        if not outline_ready:
            
            logger.info(f"Outline not ready for {item.job_id}, skipping job for now")
            # Reset status so it can be retried
            async for db in get_customer_db():
                await self.proposal_queue_service.update_proposal_queue_status(
                    db, item.job_id, "NEW", "WAITING_FOR_OUTLINE"
                )
                break
            return False

        # Generate proposal drafts using outline_service.generate_draft
        logger.info(f"Generating proposal drafts using outline service for {'RFP' if is_rfp else 'RFI'}")
        await self._generate_proposal_drafts_with_outline_service(
            item.opps_id,
            item.tenant_id,
            job.get("opportunityType", ""),
            job_instruction,
            job_requested_by
        )

        logger.info(f"Completed processing proposal item: {item.job_id}")
        
        return True

        ## Send Notifications to all Tenant Admins

    def _determine_source_type(self, opportunity_type: str) -> str:
        """Determine source type from opportunity type"""
        if not opportunity_type:
            return "custom"

        opportunity_type = opportunity_type.lower()
        if opportunity_type in ["sam"]:
            return "sam"
        elif opportunity_type in ["ebuy"]:
            return "ebuy"
        else:
            return "custom"

    async def _check_existing_outlines_in_db(self, opps_id: str, tenant_id: str, source: str) -> bool:
        """Check if outlines already exist in the database"""
        try:
            if source == "sam":
                from models.kontratar_models import OppsTable
                async for db in get_kontratar_db():
                    query = select(OppsTable).where(OppsTable.notice_id == opps_id)
                    result = await db.execute(query)
                    record = result.scalar_one_or_none()
                    if record and any(
                        getattr(record, f"proposal_outline_{i}") is not None and
                        getattr(record, f"proposal_outline_{i}") != ""
                        for i in range(1, 6)
                    ):
                        return True
                    break
            elif source == "ebuy":
                try:
                    from models.kontratar_models import EBUYOppsTable
                    async for db in get_kontratar_db():
                        query = select(EBUYOppsTable).where(EBUYOppsTable.rfq_id == opps_id)
                        result = await db.execute(query)
                        record = result.scalar_one_or_none()
                        if record:
                            # Check for proposal_outline column (single column)
                            if hasattr(record, 'proposal_outline') and getattr(record, "proposal_outline") is not None and getattr(record, "proposal_outline") != "":
                                return True
                            # Check for multiple proposal_outline columns (like SAM table)
                            if any(
                                hasattr(record, f"proposal_outline_{i}") and
                                getattr(record, f"proposal_outline_{i}") is not None and
                                getattr(record, f"proposal_outline_{i}") != ""
                                for i in range(1, 6)
                            ):
                                return True
                        break
                except Exception as ebuy_error:
                    logger.warning(f"Error checking EBUY outlines for {opps_id}: {ebuy_error}")
            elif source == "custom":
                from models.customer_models import CustomOppsTable
                async for db in get_customer_db():
                    query = select(CustomOppsTable).where(CustomOppsTable.opportunity_id == opps_id)
                    result = await db.execute(query)
                    record = result.scalar_one_or_none()
                    if record and any(
                        getattr(record, f"proposal_outline_{i}") is not None and
                        getattr(record, f"proposal_outline_{i}") != ""
                        for i in range(1, 6)
                    ):
                        return True
                    break
            return False
        except Exception as e:
            logger.error(f"Error checking existing outlines in DB for {opps_id}: {e}")
            return False

    async def _check_outline_queue_status(self, opps_id: str, tenant_id: str) -> str:
        """Check the status of outline in proposal_outline_queue"""
        try:
            async for db in get_kontratar_db():
                query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.first_request == True
                ).order_by(ProposalOutlineQueue.created_date.desc())
                result = await db.execute(query)
                queue_item = result.scalar_one_or_none()
                if queue_item:
                    return queue_item.status
                break
            return "NOT_FOUND"
        except Exception as e:
            logger.error(f"Error checking outline queue status for {opps_id}: {e}")
            return "ERROR"

    async def _update_outline_queue_status(self, opps_id: str, tenant_id: str, new_status: str):
        """Update the status of an outline queue item"""
        try:
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.first_request == True
                ).values(
                    status=new_status,
                    last_updated_date=datetime.now(),
                    error_message=None if new_status != "FAILED" else ProposalOutlineQueue.error_message
                )
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue status for {opps_id} to {new_status}")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue status for {opps_id}: {e}")

    async def _update_outline_queue_timestamp(self, opps_id: str, tenant_id: str):
        """Update the timestamp of an outline queue item to prioritize it"""
        try:
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.first_request == True
                ).values(last_updated_date=datetime.now())
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue timestamp for {opps_id} to prioritize processing")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue timestamp for {opps_id}: {e}")

    async def _add_to_outline_queue(self, opps_id: str, tenant_id: str, source: str):
        """Add opportunity to proposal_outline_queue"""
        try:
            async for db in get_kontratar_db():
                # Check if already exists to avoid duplicates
                existing_query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.first_request == True,
                    ProposalOutlineQueue.status.in_(["NEW", "PROCESSING"])
                )
                existing_result = await db.execute(existing_query)
                existing_item = existing_result.scalar_one_or_none()

                if not existing_item:
                    new_queue_item = ProposalOutlineQueue(
                        opps_id=opps_id,
                        tenant_id='SYSTEM',
                        status="NEW",
                        outline_type=source.lower(),
                        first_request=True
                    )
                    db.add(new_queue_item)
                    await db.commit()
                    logger.info(f"Added {opps_id} to proposal_outline_queue")
                else:
                    logger.info(f"Outline queue item already exists for {opps_id}")
                break
        except Exception as e:
            logger.error(f"Error adding {opps_id} to outline queue: {e}")

    async def _check_outline_availability(self, opps_id: str, tenant_id: str, opportunity_type: str) -> bool:
        """
        Check if outline is available for the proposal generation.
        Handles all outline queue status updates and management.

        Returns:
            True if outline is ready, False if need to wait/skip
        """
        try:
            # Determine source type from opportunity_type
            source = self._determine_source_type(opportunity_type)
            logger.info(f"Checking outline availability for {opps_id}, source: {source}")

            # Check if outline already exists in database
            outline_exists = await self._check_existing_outlines_in_db(opps_id, tenant_id, source)
            if outline_exists:
                logger.info(f"Outline already exists in DB for {opps_id}")
                return True

            # Check proposal_outline_queue status
            queue_status = await self._check_outline_queue_status(opps_id, tenant_id)

            if queue_status == "COMPLETED":
                logger.info(f"Outline generation completed for {opps_id}")
                return True
            elif queue_status == "PROCESSING" or queue_status == "CLAIMED":
                logger.info(f"Outline generation in progress for {opps_id}, will wait for next schedule")
                return False
            elif queue_status == "FAILED":
                logger.info(f"Outline generation failed for {opps_id}, updating to NEW status")
                await self._update_outline_queue_status(opps_id, tenant_id, "NEW")
                return False
            elif queue_status == "NEW":
                logger.info(f"Outline generation queued but not started for {opps_id}, updating timestamp")
                await self._update_outline_queue_timestamp(opps_id, tenant_id)
                return False
            else:
                # NOT_FOUND or ERROR - add to queue
                logger.info(f"Adding {opps_id} to outline queue")
                await self._add_to_outline_queue(opps_id, tenant_id, source)
                return False

        except Exception as e:
            logger.error(f"Error checking outline availability for {opps_id}: {e}")
            return False

    def enable(self):
        """Enable the proposal scheduler (allows jobs to run)"""
        self.is_enabled = True
        logger.info("Proposal scheduler enabled")
    
    def disable(self):
        """Disable the proposal scheduler (prevents jobs from running)"""
        self.is_enabled = False
        logger.info("Proposal scheduler disabled")
    
    def is_scheduler_enabled(self) -> bool:
        """Check if proposal scheduler is enabled"""
        return self.is_enabled
    
    def start(self, interval_seconds: int = 30):
        """Start the proposal scheduler"""
        if self.is_running:
            logger.warning("Proposal scheduler is already running")
            return
        
        # Add job to the scheduler
        self.scheduler.add_job(
            self.process_proposal_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_proposal_queue",
            name="Process Proposal Queue"
        )
        
        # Start the scheduler
        self.scheduler.start()
        self.is_running = True
        
        logger.info(f"Proposal scheduler started with {interval_seconds} second interval")
    
    def stop(self):
        """Stop the proposal scheduler"""
        if not self.is_running:
            logger.warning("Proposal scheduler is not running")
            return
        
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Proposal scheduler stopped")
    
    def restart(self, interval_seconds: int = 30):
        """Restart the proposal scheduler"""
        logger.info("Restarting proposal scheduler...")
        self.stop()
        self.start(interval_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive proposal scheduler status"""
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        """Get information about scheduled jobs"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs

    async def _generate_proposal_drafts_with_outline_service(
        self,
        opps_id: str,
        tenant_id: str,
        opportunity_type: str,
        job_instruction: str,
        job_submitted_by: str
    ):
        """
        Generate proposal drafts
        """
        try:
            source = self._determine_source_type(opportunity_type)
            logger.info(f"Generating drafts for opportunity {opps_id} with source {source}")

            # Parse job instruction to get necessary parameters
            job = json.loads(job_instruction)
            client_short_name = job.get("clientShortName", "")
            set_for_review = job.get("setForReview", True)
            cover_page = job.get("coverPage", None)
            export_type = job.get("exportType", None)
            requested_volumes = job.get("generatedVolumes", [1, 2, 3, 4, 5])

            # Get tenant metadata
            tenant_metadata = ""
            async for db in get_customer_db():
                tenant = await TenantController.get_by_tenant_id(db, tenant_id)
                tenant_metadata = f"{tenant}" if tenant else ""
                break

            # Get all table of contents and outlines from database
            all_table_of_contents = await self._get_all_table_of_contents(opps_id, tenant_id, source)
            all_outlines = await self._get_all_outlines(opps_id, tenant_id, source)

            # Generate drafts for each volume using outline_service.generate_draft
            proposal_volumes: List[List[Dict[str, Any]] | None] = []

            for idx, table_of_contents in enumerate(all_table_of_contents):
                current_volume = idx + 1
                logger.info(f"Processing volume {current_volume}")

                # Skip current volume if it is not part of requested volumes
                if current_volume not in requested_volumes:
                    logger.info(f"Skipping volume {current_volume} as it is not in requested_volumes")
                    proposal_volumes.append(None)
                    continue

                # Skip if no table of contents available
                if table_of_contents is None or len(table_of_contents) == 0:
                    logger.warning(f"No table of contents for volume {current_volume}, skipping")
                    proposal_volumes.append(None)
                    continue

                # Generate draft for this volume using outline_service.generate_draft
                logger.info(f"Generating draft for volume {current_volume} using outline service")
                draft_result = await self.outline_service.generate_draft(
                    opportunity_id=opps_id,
                    tenant_id=tenant_id,
                    source=source,
                    client_short_name=client_short_name,
                    tenant_metadata=tenant_metadata,
                    table_of_contents=table_of_contents
                )

                # Extract draft data
                draft_data = None
                if draft_result and "draft" in draft_result:
                    draft_data = draft_result["draft"]
                    logger.info(f"✓ Draft generated successfully with {len(draft_data)} sections for volume {current_volume}")
                    proposal_volumes.append(draft_data)
                else:
                    logger.warning(f"⚠ Draft generation failed for volume {current_volume}")
                    proposal_volumes.append(None)

            # Generate constructive criticism for the proposal volumes
            logger.info(f"Generating constructive criticism for opportunity {opps_id}")

            # try:
            #     save_dir = "proposal_outputs"
            #     os.makedirs(save_dir, exist_ok=True)

            #     save_path = os.path.join(save_dir, f"{opps_id}_proposal_data.json")
            #     with open(save_path, "w", encoding="utf-8") as f:
            #         json.dump(
            #             {
            #                 "opportunity_id": opps_id,
            #                 "tenant_id": tenant_id,
            #                 "proposal_volumes": proposal_volumes,
            #                 "all_outlines": all_outlines,
            #             },
            #             f,
            #             ensure_ascii=False,
            #             indent=2
            #         )
            #     logger.info(f"✓ Saved proposal_volumes and all_outlines to {save_path}")
            # except Exception as save_error:
            #     logger.error(f"⚠ Failed to save proposal data for {opps_id}: {save_error}")
            
            try:
                # Generate criticism
                criticism_record_id = await generate_proposal_criticism(
                    opportunity_id=opps_id,
                    tenant_id=tenant_id,
                    proposal_volumes=proposal_volumes,
                    all_outlines=all_outlines,
                )
                
                if criticism_record_id:
                    logger.info(f"✓ Constructive criticism generated and stored with ID: {criticism_record_id}")
                else:
                    logger.warning(f"⚠ Failed to generate constructive criticism for {opps_id}")
                    
            except Exception as criticism_error:
                logger.error(f"Error generating constructive criticism for {opps_id}: {criticism_error}")


            # Move all volumes to review or format
            if set_for_review:
                logger.info("Moving all volumes to review...")
                await self._move_all_volumes_to_review(
                    proposal_volumes, tenant_id, opps_id, source, client_short_name, job_instruction
                )
            else:
                logger.info(f"Converting all volumes to {export_type} format and saving to datametastore...")
                if export_type == 2:  # PDF
                    await self._convert_volumes_to_pdf_bytes(
                        proposal_volumes, all_table_of_contents, tenant_id, opps_id, source,
                        client_short_name, cover_page, job_submitted_by
                    )
                elif export_type == 1:  # DOCX
                    await self._convert_volumes_to_docx_bytes(
                        proposal_volumes, all_table_of_contents, tenant_id, opps_id, source,
                        client_short_name, cover_page, job_submitted_by
                    )
                else:
                    logger.warning(f"Unknown export type: {export_type}, defaulting to PDF")
                    await self._convert_volumes_to_pdf_bytes(
                        proposal_volumes, all_table_of_contents, tenant_id, opps_id, source,
                        client_short_name, cover_page, job_submitted_by
                    )

                logger.info("Moving all volumes to format...")
                await self._move_all_volumes_to_format(
                    proposal_volumes,tenant_id, opps_id, source, client_short_name, cover_page, export_type, job_submitted_by
                    )


            logger.info(f"Successfully completed draft generation for opportunity {opps_id}")

        except Exception as e:
            logger.error(f"Error generating proposal drafts for opportunity {opps_id}: {e}")
            raise

    async def _get_all_table_of_contents(self, opps_id: str, tenant_id: str, source: str) -> List[List[Dict[str, Any]] | None]:
        """Get all table of contents from database"""
        try:
            record = None
            if source == "custom":
                async for db in get_customer_db():
                    record = await CustomOpportunitiesController.get_by_opportunity_id(db, opps_id)
                    break
            elif source == "sam":
                async for db in get_kontratar_db():
                    record = await OppsController.get_by_opportunity_id(db, opps_id)
                    break
            else:
                logger.warning(f"Source {source} not fully implemented for TOC retrieval")
                return [None] * 5

            if not record:
                logger.warning(f"No record found for opportunity {opps_id}")
                return [None] * 5

            table_of_contents = []

            # Get TOC 1
            toc_1 = record.toc_text
            if toc_1 is not None and str(toc_1).strip():
                try:
                    table_of_contents.append(json.loads(str(toc_1)))
                except (json.JSONDecodeError, ValueError):
                    logger.warning(f"Invalid JSON in toc_1: {toc_1}")
                    table_of_contents.append(None)
            else:
                table_of_contents.append(None)

            # Get TOC 2-5
            for i in range(2, 6):
                toc_attr = getattr(record, f"toc_text_{i}", None)
                if toc_attr is not None and str(toc_attr).strip():
                    try:
                        table_of_contents.append(json.loads(str(toc_attr)))
                    except (json.JSONDecodeError, ValueError):
                        logger.warning(f"Invalid JSON in toc_text_{i}: {toc_attr}")
                        table_of_contents.append(None)
                else:
                    table_of_contents.append(None)

            return table_of_contents

        except Exception as e:
            logger.error(f"Error getting table of contents for {opps_id}: {e}")
            return [None] * 5

    async def _get_all_outlines(self, opps_id: str, tenant_id: str, source: str) -> List[List[Dict[str, Any]] | None]:
        """Get all outlines from database"""
        try:
            record = None
            if source == "custom":
                async for db in get_customer_db():
                    record = await CustomOpportunitiesController.get_all_proposal_outlines(db, opps_id)
                    break
            elif source == "sam":
                async for db in get_customer_db():
                    record = await OppsController.get_all_proposal_outlines(db, opps_id)
                    break
            else:
                logger.warning(f"Source {source} not fully implemented for outline retrieval")
                return [None] * 5

            if not record:
                logger.warning(f"No record found for opportunity {opps_id}")
                return [None] * 5

            return record

        except Exception as e:
            logger.error(f"Error getting outlines for {opps_id}: {e}")
            return [None] * 5

    async def _move_all_volumes_to_review(
        self,
        all_volumes: List[List[Dict[str, Any]] | None],
        tenant_id: str,
        opps_id: str,
        source: str,
        client_short_name: str,
        job_instruction: str
    ):
        """Move all proposal volumes to review"""
        logger.info(f"Moving all volumes to review for opportunity {opps_id}")
        # Use the RFP generation service's move_all_volumes_to_review method
        await self.rfp_generation_service.move_all_volumes_to_review(
            all_volumes=all_volumes,
            tenant_id=tenant_id,
            opportunity_id=opps_id,
            source=source,
            client_short_name=client_short_name,
            job_instruction=job_instruction
        )

    async def _move_all_volumes_to_format(
        self,
        all_volumes: List[List[Dict[str, Any]] | None],
        tenant_id: str,
        opps_id: str,
        source: str,
        client_short_name: str,
        cover_page: int,
        export_type: int,
        job_submitted_by: str
    ):
        """Move all proposal volumes to format"""
        logger.info(f"Moving all volumes to format for opportunity {opps_id}")
        # Use the RFP generation service's move_all_volumes_to_format method
        await self.rfp_generation_service.move_all_volumes_to_format(
            all_volumes=all_volumes,
            tenant_id=tenant_id,
            opportunity_id=opps_id,
            source=source,
            client_short_name=client_short_name,
            cover_page=cover_page,
            format_type=export_type,
            job_submitted_by=job_submitted_by
        )

    async def _convert_volumes_to_pdf_bytes(
        self,
        all_volumes: List[List[Dict[str, Any]] | None],
        all_table_of_contents: List[List[Dict[str, Any]] | None],
        tenant_id: str,
        opps_id: str,
        source: str,
        client_short_name: str,
        cover_page_id: Optional[int],
        job_submitted_by: str
    ):
        """Convert all proposal volumes to PDF bytes and save to datametastore"""
        try:
            logger.info(f"Converting {len(all_volumes)} volumes to PDF bytes for opportunity {opps_id}")
            # Initialize title extraction service
            title_service = DocumentTitleExtractionService()

            # Get necessary details for PDF generation
            opportunity_details = None
            if source == "custom":
                async for db in get_customer_db():
                    opportunity_details = await CustomOpportunitiesController.get_by_opportunity_id(db, opps_id)
                    break
            elif source == "sam":
                async for db in get_kontratar_db():
                    opportunity_details = await OppsController.get_by_opportunity_id(db, opps_id)
                    break

            # Get tenant details
            tenant_details = None
            async for db in get_customer_db():
                tenant_details = await TenantController.get_by_tenant_id(db, tenant_id)
                break

            # Get user details (using a default user ID for system-generated PDFs)
            user_details = None
            async for db in get_customer_db():
                user_details = await RfpDraftExportController.get_user_by_id(db, 69)  # System user
                break

            # Get cover page if provided
            cover_page = None
            if cover_page_id:
                async for db in get_customer_db():
                    cover_page = await DataMetastoreController.get_by_id(db, cover_page_id)
                    break

            # Process each volume
            for volume_idx, volume_data in enumerate(all_volumes):
                volume_number = volume_idx + 1

                # Skip if volume is None or empty
                if not volume_data:
                    logger.info(f"Skipping volume {volume_number} - no data")
                    continue

                logger.info(f"Processing volume {volume_number} with {len(volume_data)} sections")

                try:
                    # Convert volume data to markdown
                    markdown_content = MarkdownRenderer.convert_draft_to_markdown(
                        volume_data, opportunity_details, user_details, tenant_details
                    )

                    # Get table of contents for this volume
                    volume_toc = None
                    volume_title = f"Volume_{volume_number}"
                    if volume_idx < len(all_table_of_contents) and all_table_of_contents[volume_idx]:
                        volume_toc = all_table_of_contents[volume_idx]
                        # Extract volume title from TOC if available
                        if volume_toc and len(volume_toc) > 0:
                            # Look for volume title in the first TOC entry or in a title field
                            if isinstance(volume_toc[0], dict):
                                if 'volume_title' in volume_toc[0]:
                                    volume_title = volume_toc[0]['volume_title']
                                elif 'title' in volume_toc[0] and 'volume' in volume_toc[0]['title'].lower():
                                    volume_title = volume_toc[0]['title']
                            # Clean up volume title for filename (remove special characters)
                            volume_title = "".join(c for c in volume_title if c.isalnum() or c in (' ', '_', '-')).strip()
                            volume_title = volume_title.replace(' ', '_')

                    document_title = await title_service.extract_document_title(
                        opportunity_data=dict(opportunity_details) if opportunity_details else {},
                        volume_number=volume_number,
                        volume_title=volume_title,
                        source=source
                    )

                    # Create cover page elements
                    cover_page_elements = None
                    if cover_page:
                        cover_page_elements = PDFGeneratorBytes.create_cover_page_elements(
                            cover_page=cover_page,
                            tenant_details=tenant_details,
                            opportunity_details=opportunity_details,
                            user_details=user_details,
                            compliance=None,
                            image_only=False
                        )

                    # Generate PDF bytes
                    pdf_bytes, success_message = PDFGeneratorBytes.generate_pdf(
                        markdown_content=markdown_content,
                        opportunity_id=opps_id,
                        tenant_id=tenant_id,
                        cover_page_elements=cover_page_elements,
                        toc_data=volume_toc,
                        trailing_page_markdown=None,
                        compliance=None,
                        volume_number=volume_number,
                        image_only=False
                    )

                    print(f"PDF_BYTES: {pdf_bytes}")

                    logger.info(f"Generated PDF bytes for volume {volume_number}: {len(pdf_bytes)} bytes")

                    # Save to datametastore
                    pdf_record_identifier = f"{opps_id}_pdf_vol_{volume_number}"

                    async for db in get_customer_db():
                        # Check if record already exists
                        existing_record = await DataMetastoreController.get_by_record_identifier(
                            db, pdf_record_identifier
                        )

                        if existing_record:
                            # Update existing record
                            await DataMetastoreController.update(
                                db=db,
                                record_id=existing_record.id,
                                original_document=pdf_bytes,
                                original_document_file_name=f"{document_title}_volume_{volume_number}.pdf",
                                owner=job_submitted_by
                            )
                            logger.info(f"Updated existing PDF record for opportunity {opps_id}, volume {volume_number}")
                        else:
                            # Create new record
                            new_record = await DataMetastoreController.add(
                                db=db,
                                record_identifier=pdf_record_identifier,
                                record_type="PROPOSAL_PDF",
                                tenant_id=tenant_id,
                                original_document_content_type="application/pdf",
                                original_document_file_name=f"{document_title}_volume_{volume_number}.pdf",
                                original_document=pdf_bytes,
                                owner=job_submitted_by
                            )
                            if new_record:
                                logger.info(f"Created new PDF record for opportunity {opps_id}, volume {volume_number} with ID: {new_record.id}")
                            else:
                                logger.error(f"Failed to create PDF record for opportunity {opps_id}, volume {volume_number}")
                        break

                except Exception as volume_error:
                    logger.error(f"Error processing volume {volume_number} for opportunity {opps_id}: {volume_error}")
                    continue

            logger.info(f"Completed PDF conversion for all volumes of opportunity {opps_id}")

        except Exception as e:
            logger.error(f"Error converting volumes to PDF bytes for opportunity {opps_id}: {e}")
            raise

    async def _convert_volumes_to_docx_bytes(
        self,
        all_volumes: List[List[Dict[str, Any]] | None],
        all_table_of_contents: List[List[Dict[str, Any]] | None],
        tenant_id: str,
        opps_id: str,
        source: str,
        client_short_name: str,
        cover_page_id: Optional[int],
        job_submitted_by: str
    ):
        """Convert all proposal volumes to DOCX bytes and save to datametastore"""
        try:
            logger.info(f"Converting {len(all_volumes)} volumes to DOCX bytes for opportunity {opps_id}")
            # Initialize title extraction service
            title_service = DocumentTitleExtractionService()

            # Get necessary details for DOCX generation
            opportunity_details = None
            if source == "custom":
                async for db in get_customer_db():
                    opportunity_details = await CustomOpportunitiesController.get_by_opportunity_id(db, opps_id)
                    break
            elif source == "sam":
                async for db in get_kontratar_db():
                    opportunity_details = await OppsController.get_by_opportunity_id(db, opps_id)
                    break

            # Get tenant details
            tenant_details = None
            async for db in get_customer_db():
                tenant_details = await TenantController.get_by_tenant_id(db, tenant_id)
                break

            # Get user details (using a default user ID for system-generated documents)
            user_details = None
            async for db in get_customer_db():
                user_details = await RfpDraftExportController.get_user_by_id(db, 69)  # System user
                break

            # Get cover page if provided
            cover_page = None
            if cover_page_id:
                async for db in get_customer_db():
                    cover_page = await DataMetastoreController.get_by_id(db, cover_page_id)
                    break

            # Process each volume
            for volume_idx, volume_data in enumerate(all_volumes):
                volume_number = volume_idx + 1

                # Skip if volume is None or empty
                if not volume_data:
                    logger.info(f"Skipping volume {volume_number} - no data")
                    continue

                logger.info(f"Processing volume {volume_number} with {len(volume_data)} sections")

                try:
                    # Get table of contents for this volume
                    volume_toc = None
                    volume_title = f"Volume_{volume_number}"
                    if volume_idx < len(all_table_of_contents) and all_table_of_contents[volume_idx]:
                        volume_toc = all_table_of_contents[volume_idx]
                        # Extract volume title from TOC if available
                        if volume_toc and len(volume_toc) > 0:
                            # Look for volume title in the first TOC entry or in a title field
                            if isinstance(volume_toc[0], dict):
                                if 'volume_title' in volume_toc[0]:
                                    volume_title = volume_toc[0]['volume_title']
                                elif 'title' in volume_toc[0] and 'volume' in volume_toc[0]['title'].lower():
                                    volume_title = volume_toc[0]['title']
                            # Clean up volume title for filename (remove special characters)
                            volume_title = "".join(c for c in volume_title if c.isalnum() or c in (' ', '_', '-')).strip()
                            volume_title = volume_title.replace(' ', '_')

                    # Extract intelligent document title
                    document_title = await title_service.extract_document_title(
                        opportunity_data=dict(opportunity_details) if opportunity_details else {},
                        volume_number=volume_number,
                        volume_title=volume_title,
                        source=source
                    )

                    # Convert volume data to markdown
                    markdown_content = MarkdownRenderer.convert_draft_to_markdown(
                        volume_data, opportunity_details, user_details, tenant_details
                    )

                    # Generate DOCX bytes
                    docx_bytes, success_message = DocxBytesGenerator.generate_docx_bytes(
                        markdown_content=markdown_content,
                        opportunity_id=opps_id,
                        tenant_id=tenant_id,
                        cover_page=cover_page,
                        tenant_details=tenant_details,
                        opportunity_details=opportunity_details,
                        user_details=user_details,
                        toc_data=volume_toc,
                        trailing_page_markdown=None,
                        compliance=None,
                        volume_number=volume_number,
                        document_title=document_title
                    )
                    print(f"DOCX_BYTES: {docx_bytes}")

                    logger.info(f"Generated DOCX bytes for volume {volume_number}: {len(docx_bytes)} bytes")

                    # Save to datametastore
                    docx_record_identifier = f"{opps_id}_docx_vol_{volume_number}"

                    async for db in get_customer_db():
                        # Check if record already exists
                        existing_record = await DataMetastoreController.get_by_record_identifier(
                            db, docx_record_identifier
                        )

                        if existing_record:
                            # Update existing record
                            await DataMetastoreController.update(
                                db=db,
                                record_id=existing_record.id,
                                original_document=docx_bytes,
                                original_document_file_name=f"{document_title}_volume_{volume_number}.docx",
                                owner=job_submitted_by
                            )
                            logger.info(f"Updated existing DOCX record for opportunity {opps_id}, volume {volume_number}")
                        else:
                            # Create new record
                            new_record = await DataMetastoreController.add(
                                db=db,
                                record_identifier=docx_record_identifier,
                                record_type="PROPOSAL_DOCX",
                                tenant_id=tenant_id,
                                original_document_content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                original_document_file_name=f"{document_title}_volume_{volume_number}.docx",
                                original_document=docx_bytes,
                                owner=job_submitted_by
                            )
                            if new_record:
                                logger.info(f"Created new DOCX record for opportunity {opps_id}, volume {volume_number} with ID: {new_record.id}")
                            else:
                                logger.error(f"Failed to create DOCX record for opportunity {opps_id}, volume {volume_number}")
                        break

                except Exception as volume_error:
                    logger.error(f"Error processing volume {volume_number} for opportunity {opps_id}: {volume_error}")
                    continue

            logger.info(f"Completed DOCX conversion for all volumes of opportunity {opps_id}")

        except Exception as e:
            logger.error(f"Error converting volumes to DOCX bytes for opportunity {opps_id}: {e}")
            raise